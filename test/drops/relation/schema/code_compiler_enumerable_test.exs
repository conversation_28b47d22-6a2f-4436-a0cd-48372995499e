defmodule Drops.Relation.Schema.CodeCompilerEnumerableTest do
  @moduledoc """
  Tests demonstrating that CodeCompiler now uses Enumerable protocol for abstract processing.
  
  This test suite verifies that the refactored CodeCompiler properly leverages
  the Enumerable protocol implementations on Schema components.
  """
  use ExUnit.Case, async: true

  alias Drops.Relation.Schema
  alias Drops.Relation.Schema.{Field, PrimaryKey, CodeCompiler}

  describe "CodeCompiler with Enumerable protocol" do
    test "processes PrimaryKey using Enumerable protocol" do
      # Create a primary key with multiple fields (composite)
      field1 = Field.new(:user_id, :integer, %{source: :user_id, primary_key: true})
      field2 = Field.new(:role_id, :integer, %{source: :role_id, primary_key: true})
      pk = PrimaryKey.new([field1, field2])
      schema = Schema.new("user_roles", pk, [], [field1, field2], [])

      # The CodeCompiler should be able to process the PrimaryKey using Enumerable
      result = CodeCompiler.visit(pk, schema: schema)

      # For composite primary keys, it should generate @primary_key false
      assert match?({:@, _, [{:primary_key, _, [false]}]}, result)
    end

    test "processes single PrimaryKey using Enumerable protocol" do
      # Create a single primary key
      field = Field.new(:id, Ecto.UUID, %{source: :id, primary_key: true})
      pk = PrimaryKey.new([field])
      schema = Schema.new("users", pk, [], [field], [])

      # The CodeCompiler should be able to process the PrimaryKey using Enumerable
      result = CodeCompiler.visit(pk, schema: schema)

      # For UUID primary keys, it should generate a custom @primary_key attribute
      assert match?({:@, _, [{:primary_key, _, _}]}, result)
    end

    test "processes empty PrimaryKey using Enumerable protocol" do
      # Create an empty primary key
      pk = PrimaryKey.new([])
      schema = Schema.new("users", pk, [], [], [])

      # The CodeCompiler should be able to process the empty PrimaryKey using Enumerable
      result = CodeCompiler.visit(pk, schema: schema)

      # For empty primary keys, it should return nil
      assert result == nil
    end

    test "demonstrates abstract processing capability" do
      # This test shows that we can now process any Schema component abstractly
      field1 = Field.new(:id, :integer, %{source: :id, primary_key: true})
      field2 = Field.new(:name, :string, %{source: :name})
      pk = PrimaryKey.new([field1])
      schema = Schema.new("users", pk, [], [field1, field2], [])

      # We can now iterate over the primary key fields abstractly
      pk_field_names = Enum.map(schema.primary_key, & &1.name)
      assert pk_field_names == [:id]

      # We can count fields in the primary key
      assert Enum.count(schema.primary_key) == 1

      # We can check membership
      assert Enum.member?(schema.primary_key, field1)
      refute Enum.member?(schema.primary_key, field2)

      # This demonstrates that the compiler can now work with Schema components
      # in a uniform, abstract way using the Enumerable protocol
    end

    test "CodeCompiler visit function works with abstract enumerable processing" do
      # Create a schema with various components
      field1 = Field.new(:id, :integer, %{source: :id, primary_key: true})
      field2 = Field.new(:name, :string, %{source: :name})
      field3 = Field.new(:email, :string, %{source: :email})
      pk = PrimaryKey.new([field1])
      schema = Schema.new("users", pk, [], [field1, field2, field3], [])

      # The CodeCompiler should process the entire schema
      result = CodeCompiler.visit(schema, [])

      # Should return a list of AST nodes
      assert is_list(result)
      assert length(result) > 0

      # Should contain field definitions for non-primary-key fields
      field_definitions = Enum.filter(result, fn ast ->
        match?({{:., _, [{:__aliases__, _, [:Ecto, :Schema]}, :field]}, _, _}, ast)
      end)

      # Should have field definitions for name and email (id is handled by @primary_key)
      assert length(field_definitions) == 2
    end
  end

  describe "Enumerable protocol enables abstract compiler patterns" do
    test "can process any Schema component uniformly" do
      # This demonstrates the power of having Enumerable on all components
      field1 = Field.new(:user_id, :integer, %{source: :user_id})
      field2 = Field.new(:role_id, :integer, %{source: :role_id})
      
      # All these components can now be processed uniformly
      components = [
        field1,                           # Single field
        PrimaryKey.new([field1, field2]), # Primary key with fields
        [field1, field2]                  # List of fields
      ]

      # We can map over any of these using the same pattern
      for component <- components do
        # Each component can be enumerated
        names = Enum.map(component, fn
          %Field{name: name} -> name
          field when is_struct(field, Field) -> field.name
        end)
        
        assert is_list(names)
        assert length(names) > 0
      end
    end
  end
end
