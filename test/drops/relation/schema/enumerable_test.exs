defmodule Drops.Relation.Schema.EnumerableTest do
  @moduledoc """
  Tests for Enumerable protocol implementations on Schema components.
  
  This test suite verifies that all Schema components properly implement
  the Enumerable protocol, enabling abstract processing in compilers.
  """
  use ExUnit.Case, async: true

  alias Drops.Relation.Schema
  alias Drops.Relation.Schema.{Field, PrimaryKey, ForeignKey, Index, Indices}

  describe "Field Enumerable" do
    test "implements Enumerable protocol correctly" do
      field = Field.new(:name, :string, %{source: :name})

      # Test count
      assert Enum.count(field) == 1

      # Test member?
      assert Enum.member?(field, field)
      refute Enum.member?(field, Field.new(:email, :string, %{}))

      # Test reduce/map
      result = Enum.map(field, & &1.name)
      assert result == [:name]

      # Test to_list
      assert Enum.to_list(field) == [field]
    end
  end

  describe "PrimaryKey Enumerable" do
    test "implements Enumerable protocol for single primary key" do
      field = Field.new(:id, :integer, %{source: :id})
      pk = PrimaryKey.new([field])

      # Test count
      assert Enum.count(pk) == 1

      # Test member?
      assert Enum.member?(pk, field)
      refute Enum.member?(pk, Field.new(:name, :string, %{}))

      # Test reduce/map
      result = Enum.map(pk, & &1.name)
      assert result == [:id]

      # Test to_list
      assert Enum.to_list(pk) == [field]
    end

    test "implements Enumerable protocol for composite primary key" do
      field1 = Field.new(:user_id, :integer, %{source: :user_id})
      field2 = Field.new(:role_id, :integer, %{source: :role_id})
      pk = PrimaryKey.new([field1, field2])

      # Test count
      assert Enum.count(pk) == 2

      # Test member?
      assert Enum.member?(pk, field1)
      assert Enum.member?(pk, field2)
      refute Enum.member?(pk, Field.new(:name, :string, %{}))

      # Test reduce/map
      result = Enum.map(pk, & &1.name)
      assert result == [:user_id, :role_id]

      # Test to_list
      assert Enum.to_list(pk) == [field1, field2]
    end

    test "implements Enumerable protocol for empty primary key" do
      pk = PrimaryKey.new([])

      # Test count
      assert Enum.count(pk) == 0

      # Test reduce/map
      result = Enum.map(pk, & &1.name)
      assert result == []

      # Test to_list
      assert Enum.to_list(pk) == []
    end
  end

  describe "ForeignKey Enumerable" do
    test "implements Enumerable protocol correctly" do
      fk = ForeignKey.new(:user_id, "users", :id, :user)

      # Test count
      assert Enum.count(fk) == 1

      # Test member?
      assert Enum.member?(fk, fk)
      refute Enum.member?(fk, ForeignKey.new(:role_id, "roles", :id, :role))

      # Test reduce/map
      result = Enum.map(fk, & &1.field)
      assert result == [:user_id]

      # Test to_list
      assert Enum.to_list(fk) == [fk]
    end
  end

  describe "Index Enumerable" do
    test "implements Enumerable protocol for single field index" do
      field = Field.new(:email, :string, %{source: :email})
      index = Index.new("users_email_index", [field], true, :btree)

      # Test count
      assert Enum.count(index) == 1

      # Test member?
      assert Enum.member?(index, field)
      refute Enum.member?(index, Field.new(:name, :string, %{}))

      # Test reduce/map
      result = Enum.map(index, & &1.name)
      assert result == [:email]

      # Test to_list
      assert Enum.to_list(index) == [field]
    end

    test "implements Enumerable protocol for composite index" do
      field1 = Field.new(:name, :string, %{source: :name})
      field2 = Field.new(:email, :string, %{source: :email})
      index = Index.new("users_name_email_index", [field1, field2], false, :btree)

      # Test count
      assert Enum.count(index) == 2

      # Test member?
      assert Enum.member?(index, field1)
      assert Enum.member?(index, field2)
      refute Enum.member?(index, Field.new(:id, :integer, %{}))

      # Test reduce/map
      result = Enum.map(index, & &1.name)
      assert result == [:name, :email]

      # Test to_list
      assert Enum.to_list(index) == [field1, field2]
    end
  end

  describe "Indices Enumerable" do
    test "implements Enumerable protocol for multiple indices" do
      field1 = Field.new(:email, :string, %{source: :email})
      field2 = Field.new(:name, :string, %{source: :name})
      
      index1 = Index.new("users_email_index", [field1], true, :btree)
      index2 = Index.new("users_name_index", [field2], false, :btree)
      
      indices = Indices.new([index1, index2])

      # Test count
      assert Enum.count(indices) == 2

      # Test member?
      assert Enum.member?(indices, index1)
      assert Enum.member?(indices, index2)
      refute Enum.member?(indices, Index.new("other_index", [field1], false))

      # Test reduce/map
      result = Enum.map(indices, & &1.name)
      assert result == ["users_email_index", "users_name_index"]

      # Test to_list
      assert Enum.to_list(indices) == [index1, index2]
    end

    test "implements Enumerable protocol for empty indices" do
      indices = Indices.new([])

      # Test count
      assert Enum.count(indices) == 0

      # Test reduce/map
      result = Enum.map(indices, & &1.name)
      assert result == []

      # Test to_list
      assert Enum.to_list(indices) == []
    end
  end

  describe "Schema Enumerable (existing implementation)" do
    test "implements Enumerable protocol correctly" do
      field1 = Field.new(:id, :integer, %{source: :id})
      field2 = Field.new(:name, :string, %{source: :name})
      pk = PrimaryKey.new([field1])
      schema = Schema.new("users", pk, [], [field1, field2], [])

      # Test count (should count fields)
      assert Enum.count(schema) == 2

      # Test reduce/map (should iterate over {name, field} tuples)
      result = Enum.map(schema, fn {name, _field} -> name end)
      assert result == [:id, :name]

      # Test to_list (should return {name, field} tuples)
      tuples = Enum.to_list(schema)
      assert length(tuples) == 2
      assert {field1.name, field1} in tuples
      assert {field2.name, field2} in tuples
    end
  end
end
