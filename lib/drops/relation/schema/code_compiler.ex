defmodule Drops.Relation.Schema.CodeCompiler do
  @moduledoc """
  Compiler for converting Drops.Relation.Schema structures to Ecto schema AST.

  This module follows the same visitor pattern as Drops.SQL.Compiler and
  Drops.Relation.Schema.Compiler but works with Drops.Relation.Schema structs
  and converts them to quoted expressions for field definitions and attributes.

  The compiler replaces the SchemaFieldAST protocol approach with a more
  consistent compiler pattern that can recursively process schema components.

  ## Usage

      # Convert a Relation Schema to field AST
      schema = %Drops.Relation.Schema{...}
      field_asts = Drops.Relation.Schema.CodeCompiler.visit(schema, [])

  ## Examples

      iex> schema = %Drops.Relation.Schema{fields: [...], primary_key: ...}
      iex> asts = Drops.Relation.Schema.CodeCompiler.visit(schema, [])
      iex> is_list(asts)
      true
  """

  alias Drops.Relation.Schema
  alias Drops.Relation.Schema.PrimaryKey

  @doc """
  Main entry point for converting Relation Schema to field AST.

  ## Parameters

  - `schema` - A Drops.Relation.Schema struct
  - `opts` - Optional compilation options

  ## Returns

  A list of quoted expressions containing field definitions and attributes.

  ## Examples

      iex> schema = %Drops.Relation.Schema{fields: [...], ...}
      iex> asts = Drops.Relation.Schema.CodeCompiler.visit(schema, [])
      iex> is_list(asts)
      true
  """
  def visit(%Schema{} = schema, opts) do
    # Process schema using Enumerable protocol to get tuple representation
    # but keep the original schema in opts for field processing
    new_opts = Keyword.put(opts, :schema, schema)

    schema
    |> Enum.to_list()
    # Get the {:schema, components} tuple
    |> List.first()
    |> visit(new_opts)
  end

  # Visit schema tuple structure
  def visit({:schema, components}, opts) do
    # Process each component type in the schema
    Enum.reduce(components, [], fn component, acc ->
      component_ast = visit(component, opts)
      [acc, component_ast]
    end)
    |> List.flatten()
    |> Enum.reject(&is_nil/1)
  end

  # Visit primary key tuple structure
  def visit({:primary_key, [_name, columns]}, opts) when is_list(columns) do
    case length(columns) do
      0 ->
        # No primary key
        nil

      1 ->
        # Single primary key - generate @primary_key attribute if needed
        field_name = List.first(columns)
        visit_primary_key_field(field_name, opts)

      _ ->
        # Composite primary key - generate @primary_key false to disable default
        # Individual fields will be marked with primary_key: true in field definitions
        quote do
          @primary_key false
        end
    end
  end

  # Visit field tuple structure
  def visit({:field, [name, {:type, type}, {:meta, meta}]}, opts) do
    schema = opts[:schema]
    primary_key_fields = get_primary_key_field_names(schema.primary_key)
    category = determine_field_category(name, primary_key_fields)
    visit_field_with_category(name, type, meta, category, opts)
  end

  # Visit foreign key tuple structure
  def visit(
        {:foreign_key, [_field, _references_table, _references_field, _association_name]},
        _opts
      ) do
    # For now, foreign keys don't generate AST directly in field definitions
    # They would be handled by association generation in a separate phase
    nil
  end

  # Visit index tuple structure
  def visit({:index, [_name, _columns, _unique, _type]}, _opts) do
    # Indices don't generate AST in schema field definitions
    # They would be handled by migration generation
    nil
  end

  # Visit source tuple (from schema components)
  def visit({:source, _source}, _opts) do
    # Source is handled at the schema level, not in field definitions
    nil
  end

  # Visit fields list (from schema components)
  def visit({:fields, field_tuples}, opts) when is_list(field_tuples) do
    Enum.map(field_tuples, &visit(&1, opts))
  end

  # Visit foreign_keys list (from schema components)
  def visit({:foreign_keys, fk_tuples}, opts) when is_list(fk_tuples) do
    Enum.map(fk_tuples, &visit(&1, opts))
  end

  # Visit indices list (from schema components)
  def visit({:indices, index_tuples}, opts) when is_list(index_tuples) do
    Enum.map(index_tuples, &visit(&1, opts))
  end

  # Visit type tuples (parameterized types)
  def visit({type, opts}, _opts) when is_list(opts) do
    {type, opts}
  end

  def visit({type, opts}, _opts) when is_map(opts) do
    {type, Map.to_list(opts)}
  end

  # Visit atomic values (field names, types, etc.)
  def visit(value, _opts) when is_atom(value), do: value
  def visit(value, _opts) when is_binary(value), do: value
  def visit(value, _opts) when is_number(value), do: value

  # Visit enumerable structures recursively
  def visit(enumerable, opts) when is_map(enumerable) do
    # Process maps by visiting each key-value pair
    Enum.reduce(enumerable, %{}, fn {key, value}, acc ->
      visited_key = visit(key, opts)
      visited_value = visit(value, opts)
      Map.put(acc, visited_key, visited_value)
    end)
  end

  def visit(enumerable, opts) when is_list(enumerable) and not is_binary(enumerable) do
    # Process lists by visiting each element
    Enum.map(enumerable, &visit(&1, opts))
  end

  def visit(nil, _opts), do: nil

  # Fallback for other values
  def visit(value, _opts), do: value

  # Visit individual field components using tuple structure
  defp visit_field_with_category(name, type, meta, category, opts) do
    # Skip timestamp fields and single primary key fields with special types
    cond do
      name in [:inserted_at, :updated_at] ->
        nil

      category == :single_primary_key and type in [Ecto.UUID, :binary_id] ->
        # Single primary key with special type handled by @primary_key attribute only
        nil

      category == :single_primary_key and type in [:id, :integer] ->
        # Default primary key type - no field definition needed, Ecto handles it
        nil

      true ->
        # Generate field definition
        visit_field_definition(name, type, meta, category, opts)
    end
  end

  # Generate field definition AST using tuple data
  defp visit_field_definition(name, type, meta, category, _opts) do
    field_name = visit(name, [])
    {field_type, type_opts} = extract_type_and_options(type)
    field_opts = visit_field_options(name, meta, category)

    # Merge type options with field options
    all_opts = Keyword.merge(type_opts, field_opts)

    if all_opts == [] do
      quote do
        Ecto.Schema.field(unquote(field_name), unquote(field_type))
      end
    else
      quote do
        Ecto.Schema.field(unquote(field_name), unquote(field_type), unquote(all_opts))
      end
    end
  end

  # Extract type and options from parameterized types
  defp extract_type_and_options(type) do
    case type do
      {type_module, opts} when is_list(opts) ->
        {type_module, opts}

      {type_module, opts} when is_map(opts) ->
        {type_module, Map.to_list(opts)}

      _ ->
        {type, []}
    end
  end

  # Visit field options from metadata using tuple data
  defp visit_field_options(field_name, meta, category) do
    # Visit each metadata component individually
    source_opts = visit_meta_component(:source, field_name, meta)
    default_opts = visit_meta_component(:default, field_name, meta)
    pk_opts = visit_category_options(category)

    # Merge all options (type options are handled separately)
    [source_opts, default_opts, pk_opts]
    |> Enum.reduce([], &Keyword.merge/2)
  end

  # Visit individual metadata components
  defp visit_meta_component(:source, field_name, meta) do
    source = visit(Map.get(meta, :source, field_name), [])

    if source != field_name do
      [source: source]
    else
      []
    end
  end

  defp visit_meta_component(:default, _field_name, meta) do
    case Map.get(meta, :default) do
      nil -> []
      # Skip auto_increment as it's not valid for Ecto schemas
      :auto_increment -> []
      value -> [default: visit(value, [])]
    end
  end

  defp visit_meta_component(_key, _field_name, _meta), do: []

  # Visit category-specific options
  defp visit_category_options(:composite_primary_key), do: [primary_key: true]
  defp visit_category_options(_), do: []

  # Visit primary key field to generate @primary_key attribute
  defp visit_primary_key_field(field_name, opts) do
    # Get field information from schema
    schema = opts[:schema]
    field = Enum.find(schema.fields, &(&1.name == field_name))

    if field do
      # Check if this field needs a custom @primary_key attribute
      is_foreign_key = Map.get(field.meta, :foreign_key, false)

      cond do
        is_foreign_key and field.type in [:binary_id, Ecto.UUID] ->
          quote do
            @foreign_key_type :binary_id
          end

        field.type == Ecto.UUID ->
          quote do
            @primary_key {unquote(field.name), Ecto.UUID, autogenerate: true}
          end

        field.type == :binary_id ->
          quote do
            @primary_key {unquote(field.name), :binary_id, autogenerate: true}
          end

        field.type not in [:id, :integer] ->
          quote do
            @primary_key {unquote(field.name), unquote(field.type), autogenerate: true}
          end

        true ->
          # Default :id or :integer type - no attribute needed, Ecto will use default
          nil
      end
    else
      nil
    end
  end

  # Helper functions

  defp get_primary_key_field_names(%PrimaryKey{fields: fields}) when is_list(fields) do
    Enum.map(fields, & &1.name)
  end

  defp get_primary_key_field_names(_), do: []

  defp determine_field_category(field_name, primary_key_fields) do
    cond do
      field_name in primary_key_fields and length(primary_key_fields) == 1 ->
        :single_primary_key

      field_name in primary_key_fields and length(primary_key_fields) > 1 ->
        :composite_primary_key

      true ->
        :regular
    end
  end
end
